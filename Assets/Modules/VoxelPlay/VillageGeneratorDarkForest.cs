using System.Collections.Generic;
using UnityEngine;

namespace VoxelPlay {

	[CreateAssetMenu(menuName = "Voxel Play/Detail Generators/Village Generator", fileName = "VillageGenerator", order = 102)]
	public class VillageGeneratorDarkForest : VoxelPlayDetailGenerator {

		[System.Serializable]
		public struct CustomBuildingPlacement {
			public ModelDefinition building;
			public Vector2 position;
            public int rotation;
		}

		[SerializeField] public CustomBuildingPlacement[] customBuildingPlacements;

		struct BuildingStatus {
			public float height;
			public bool placementStatus;
		}


		VoxelPlayEnvironment env;
		// x,y,z chunk position  w cached terrain height
		Dictionary<Vector3d, BuildingStatus> buildingPositions;

		/// <summary>
		/// Initialization method. Called by Voxel Play at startup.
		/// </summary>
		public override void Init() {
			env = VoxelPlayEnvironment.instance;
			buildingPositions = new Dictionary<Vector3d, BuildingStatus>(100);

			// Fill models with empty blocks so they clear any terrain or vegetation inside them when placing on the world
			if (customBuildingPlacements != null && customBuildingPlacements.Length > 0) {
				for (int k = 0; k < customBuildingPlacements.Length; k++) {
					if (customBuildingPlacements[k].building != null) {
						env.ModelFillInside(customBuildingPlacements[k].building);
					}
				}
			}
		}


		/// <summary>
		/// Called by Voxel Play to inform that player has moved onto another chunk so new detail can start generating
		/// </summary>
		/// <param name="position">Current player position.</param>
		/// <param name="checkOnlyBorders">True means the player has moved to next chunk. False means player position is completely new and all chunks in
		/// range should be checked for detail in this call.</param>
		/// <param name="endTime">Provides a maximum time frame for execution this frame. Compare this with env.stopwatch milliseconds.</param>
		/// <returns><c>true</c>, if there's more work to be executed, <c>false</c> otherwise.</returns>
		public override bool ExploreArea(Vector3d position, bool checkOnlyBorders, long endTime) {
			if (customBuildingPlacements == null || customBuildingPlacements.Length == 0) return false;

			// Check each custom building placement to see if it's within range
			for (int i = 0; i < customBuildingPlacements.Length; i++) {
				Vector2 buildingPos2D = customBuildingPlacements[i].position;
				Vector3d buildingPos = new Vector3d(buildingPos2D.x, 0, buildingPos2D.y);
				Vector3d chunkPos = env.GetChunkPosition(buildingPos);

				// Check if this building position is within the exploration range
				var distance = Vector3d.Distance(position, chunkPos);
				int explorationRange = env.visibleChunksDistance + 10;
				float maxDistance = explorationRange * VoxelPlayEnvironment.CHUNK_SIZE;

				if (distance <= maxDistance) {
					BuildingStatus bs;
					if (!buildingPositions.TryGetValue(chunkPos, out bs)) {
						float h = env.GetTerrainHeight(buildingPos, false);

						if (h > env.waterLevel) {
							bs.height = h;
							bs.placementStatus = false;

							// No trees on this chunk
							VoxelChunk chunk;
							env.GetChunk(chunkPos, out chunk, false);
							if (chunk != null) {
								chunk.allowTrees = false;
							}
						} else {
							bs.placementStatus = true;
						}
						buildingPositions[chunkPos] = bs;
					}
				}
			}
			return false;
		}


		/// <summary>
		/// Fills the given chunk with detail. Filled voxels won't be replaced by the terrain generator.
		/// Use Voxel.Empty to fill with void.
		/// </summary>
		/// <param name="chunk">Chunk.</param>
		public override void AddDetail(VoxelChunk chunk) {
			if (customBuildingPlacements == null || customBuildingPlacements.Length == 0) return;

			// Check each building to see if it belongs to this chunk
			for (int i = 0; i < customBuildingPlacements.Length; i++) {
				Vector2 buildingPos2D = customBuildingPlacements[i].position;
				Vector3d buildingPos = new Vector3d(buildingPos2D.x, 0, buildingPos2D.y);
				Vector3d buildingChunkPos = env.GetChunkPosition(buildingPos);

				// If this building belongs to the current chunk
				if (buildingChunkPos.Equals(chunk.position)) {
					BuildingStatus bs;
					if (buildingPositions.TryGetValue(buildingChunkPos, out bs)) {
						// If not placed yet, PLACE IT!
						if (!bs.placementStatus) {
							bs.placementStatus = true;
							buildingPositions[buildingChunkPos] = bs;

							if (chunk.modified) continue; // Skip if user modified

							Vector3d pos = new Vector3d(buildingPos2D.x, bs.height, buildingPos2D.y);
							ModelDefinition buildingModel = customBuildingPlacements[i].building;

							if (buildingModel != null) {
								env.ModelPlace(pos, buildingModel, customBuildingPlacements[i].rotation * 90, 1f, true);
							}
						}
					}
				}
			}
		}

		private ModelDefinition GetBuildingModelForPosition(Vector3d chunkPos) {
			if (customBuildingPlacements == null) return null;

			foreach (var placement in customBuildingPlacements) {
				Vector2 buildingPos2D = placement.position;
				Vector3d buildingPos = new Vector3d(buildingPos2D.x, 0, buildingPos2D.y);
				Vector3d buildingChunkPos = env.GetChunkPosition(buildingPos);

				if (buildingChunkPos.Equals(chunkPos)) {
					return placement.building;
				}
			}
			return null;
		}




	}

}