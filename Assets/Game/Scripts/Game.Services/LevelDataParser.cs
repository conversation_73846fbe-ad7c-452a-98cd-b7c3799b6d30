using System.Linq;
using System.Text.RegularExpressions;
using Game.Core.Data;

namespace Game.Services
{
    public static class LevelDataParser
    {
        public static string ValidateLevelName(string text)
        {
            return Regex.Replace(text.Trim(), @"\s+", " ");
        }

        public static string ValidateLevelId(string text)
        {
            return Regex.Replace(text.Trim(), @"\s+", "_").ToLower();
        }

        public static LevelData ToLevelData(LevelContract levelContract)
        {
            return new LevelData
            {
                id = levelContract.id,
                name = levelContract.name,
                worldTemplateId = levelContract.worldTemplateId,
                worldExtents = levelContract.worldExtents,
                createdBy = levelContract.createdBy,
                createdByName = levelContract.createdByName,
                isPublished = levelContract.isPublished,
                gameMode = levelContract.gameMode,
                levelDefinitionId = levelContract.levelDefinitionId,
                buildingDisabled = levelContract.buildingDisabled,
                locomotionAreas = levelContract.locomotionAreas2,
                buildingAreas = levelContract.buildingAreas2,
                checkpoints = levelContract.checkpoints?.Select(data => new CheckpointListData
                {
                    leaderboardID = data.leaderboardID,
                    leaderboardPosOffset = data.leaderboardPosOffset,
                    checkpoints = data.checkpoints,
                    endSpawn = data.endSpawn
                }).ToList(),
                worldPortalPosition = levelContract.portalPos,
                monsters = levelContract.monsters,
                useBlockInventory = levelContract.useBlockInventory
            };
        }

        public static LevelMetaData ToLevelMetaData(LevelContract levelContract)
        {
            return new LevelMetaData
            {
                id = ValidateLevelId(levelContract.name),
                name = levelContract.name,
                worldTemplateId = levelContract.worldTemplateId,
                createdByName = levelContract.createdByName,
                gameMode = levelContract.gameMode
            };
        }

        public static LevelMetaData ToLevelMetaData(LevelData levelData)
        {
            return new LevelMetaData
            {
                id = levelData.id,
                name = levelData.name,
                worldTemplateId = levelData.worldTemplateId,
                createdByName = levelData.createdByName,
                gameMode = levelData.gameMode
            };
        }
    }
}