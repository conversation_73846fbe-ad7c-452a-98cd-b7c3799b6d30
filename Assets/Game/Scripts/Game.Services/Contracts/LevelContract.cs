using System;
using System.Collections.Generic;
using Game.Core.Data;
using UnityEngine;

namespace Game.Services
{
    [Serializable]
    public class LevelContract
    {
        public string id;
        public string name;
        public string createdBy;
        public string createdByName;
        public int worldTemplateId;
        public int worldExtents;
        public bool isPublished;
        public bool buildingDisabled;
        public GameMode gameMode;
        public LevelDefinitionId levelDefinitionId;
        public Vector3 portalPos;
        public List<LocomotionAreaData> locomotionAreas2;
        public List<BuildingAreaData> buildingAreas2;
        public List<CheckpointListData> checkpoints;
        public List<MonsterData> monsters;
        public bool useBlockInventory;
    }
}