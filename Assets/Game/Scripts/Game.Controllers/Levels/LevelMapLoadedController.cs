using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Views.Levels;
using Game.Views.Portals;
using Game.Views.Voxels;
using Game.Views.World;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Levels
{
    public class LevelMapLoadedController : ControllerBase
    {
        private const WorldSide WorldSide = Views.World.WorldSide.None;

        private LevelModel levelModel;
        private VoxelConfig voxelConfig;
        private VoxelSpaceManager voxelSpaceManager;
        private WorldBoundary worldBoundary;
        private LevelSpaceManager levelSpaceManager;

        private LevelPortalView Portal => levelSpaceManager.Portal;

        [Inject]
        private void Construct(LevelModel levelModel, VoxelSpaceManager voxelSpaceManager, VoxelConfig voxelConfig, WorldBoundary worldBoundary, LevelSpaceManager levelSpaceManager)
        {
            this.levelModel = levelModel;
            this.voxelConfig = voxelConfig;
            this.voxelSpaceManager = voxelSpaceManager;
            this.levelSpaceManager = levelSpaceManager;
            this.worldBoundary = worldBoundary;

            voxelSpaceManager.IsMapLoaded.Where(ok => ok).Subscribe(_ => HandleMapLoaded()).AddTo(DisposeCancellationToken);
        }

        private void HandleMapLoaded()
        {
            var extent = GetExtent();
            InitializeLevelPortal(WorldSide, extent);
            InitializeWorldBoundary(WorldSide, extent);
            DestroyVoxelsInPortalNoBuildZone().Forget();
        }

        private int GetExtent()
        {
            return levelModel.Level.worldExtents == 0 ? voxelConfig.WorldExtent : levelModel.Level.worldExtents;
        }

        private void InitializeLevelPortal(WorldSide worldSide, int shift)
        {
            if (levelModel.IsLobbyLevel)
            {
                Portal.SetActive(false);
                return;
            }

            Portal.SetActive(true);

            if (levelModel.Level.worldPortalPosition != Vector3.zero)
            {
                Portal.transform.position = levelModel.Level.worldPortalPosition;
                return;
            }

            var direction = GetDirection(worldSide);
            var shiftPosition = direction * shift;
            var raycastPosition = direction * (shift - 0.5f);
            var worldPortalYPosition = GetPortalYPosition(raycastPosition);
            var exitDoorPortalPosition = shiftPosition + Vector3.up * worldPortalYPosition;
            Portal.transform.position = exitDoorPortalPosition - Portal.EntranceCenterNode.localPosition.SetY(0);

            TrySetPortalYPosition(worldPortalYPosition);
        }

        private async UniTask DestroyVoxelsInPortalNoBuildZone()
        {
            if (levelModel.IsLobbyLevel)
            {
                return;
            }

            await UniTask.DelayFrame(36, cancellationToken: DisposeCancellationToken);

            var points = Portal.GetNoBuildZonePointsList();
            foreach (var point in points)
            {
                voxelSpaceManager.DamageVoxel(point, byte.MaxValue, false);
            }
        }

        private float GetPortalYPosition(Vector3 raycastPosition)
        {
            if (levelSpaceManager.PortalYPosition != 0)
            {
                return levelSpaceManager.PortalYPosition;
            }

            var yPosition = Mathf.Clamp(voxelSpaceManager.GetTerrainHeight(raycastPosition), 0, 100);

            if (yPosition == 0)
            {
                yPosition = 30;
            }

            return yPosition;
        }

        private void TrySetPortalYPosition(float yPosition)
        {
            if (levelSpaceManager.PortalYPosition != 0)
            {
                return;
            }

            levelSpaceManager.SetPortalYPosition(yPosition);
        }

        private Vector3 GetDirection(WorldSide worldSide)
        {
            return worldSide switch
            {
                WorldSide.Forward => Vector3.forward,
                WorldSide.Left => Vector3.left,
                WorldSide.Right => Vector3.right,
                _ => Vector3.back
            };
        }

        private void InitializeWorldBoundary(WorldSide worldSide, int extent)
        {
            if (levelModel.LevelDefinition.BoundsSettings.boundsDisabled)
            {
                worldBoundary.DisableBoundary();
                return;
            }

            var worldSize = 2 * extent;
            var worldHeight = voxelConfig.WorldHeight;
            var holeSize = levelModel.IsLobbyLevel ? Vector2.zero : Portal.EntranceSize;
            var holeHeight = levelModel.IsLobbyLevel ? 0f : Portal.EntranceCenterNode.position.y;
            worldBoundary.Initialize(worldSize, worldHeight, worldSide, holeSize, holeHeight);
        }
    }
}