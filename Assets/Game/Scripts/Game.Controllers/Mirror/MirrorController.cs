using System;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Game.Views.Levels;
using Game.Views.Lobby;
using Game.Views.Mirror;
using Modules.Core;
using VContainer;

namespace Game.Controllers.Mirror
{
    public class MirrorController : ControllerBase
    {
        private LevelModel levelModel;
        private MirrorManager mirrorManager;
        private LobbySpaceManager lobbySpaceManager;
        private LevelSpaceManager levelSpaceManager;

        [Inject]
        private void Construct(MirrorManager mirrorManager, LevelModel levelModel)
        {
            this.levelModel = levelModel;
            this.mirrorManager = mirrorManager;

            levelModel.OnLevelLoaded.Where(ok => ok).Subscribe(_ => HandleLevelLoaded()).AddTo(DisposeCancellationToken);
        }

        private void HandleLevelLoaded()
        {
            mirrorManager.DestroyAll();

            var poses = levelModel.LevelDefinition.PlaceholdersSettings.mirrorPlaceholder.transform.GetChildPoses();
            poses.ForEach(p => mirrorManager.Create(p));
        }
    }
}