using System;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Models;
using Game.Views.GadgetShop;
using Game.Views.Levels;
using Game.Views.Players;
using Game.Views.UI.Screens.Notification;
using Modules.Core;
using Modules.UI;
using VContainer;

namespace Game.Controllers.GadgetShop
{
    public class GadgetShopController : ControllerBase
    {
        private LevelModel levelModel;
        private PlayersModel playersModel;
        private EconomyModel economyModel;
        private GadgetShopManager gadgetShopManager;
        private InteractablesModel interactablesModel;
        private NotificationScreen notificationScreen;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;

        [Inject]
        private void Construct(
            LevelModel levelModel,
            PlayersModel playersModel,
            EconomyModel economyModel,
            IScreenManager screenManager,
            GadgetShopManager gadgetShopManager,
            InteractablesModel interactablesModel)
        {
            this.levelModel = levelModel;
            this.playersModel = playersModel;
            this.economyModel = economyModel;
            this.gadgetShopManager = gadgetShopManager;
            this.interactablesModel = interactablesModel;
            notificationScreen = screenManager.GetScreen<NotificationScreen>();

            playersModel.LocalPlayer.Subscribe(HandleLocalPlayer).AddTo(DisposeCancellationToken);
            levelModel.OnLevelLoaded.Where(ok => ok).Subscribe(_ => HandleLevelLoaded()).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayer(PlayerActor player)
        {
            if (player == null)
            {
                return;
            }

            gadgetShopManager.OnClicked.Subscribe(HandleClicked).AddTo(player);
        }

        private void HandleLevelLoaded()
        {
            gadgetShopManager.ClearInstances();

            var poses = levelModel.LevelDefinition.PlaceholdersSettings.gadgetShopPlaceholder.transform.GetChildPoses();
            poses.ForEach(p => gadgetShopManager.CreateInstance(p));
        }

        private void HandleClicked(GadgetInventoryView gadgetInventoryView)
        {
            ResolveInteractable(gadgetInventoryView).Forget();
        }

        private async UniTaskVoid ResolveInteractable(GadgetInventoryView gadgetInventoryView)
        {
            var data = gadgetInventoryView.Data;
            var interactor = gadgetInventoryView.GrabInteractor;

            if (!interactablesModel.CanCreateNewInteractable)
            {
                notificationScreen.Show("You have exceeded the maximum number of items.\nCollect your items and put them to the shredder", 5);
                return;
            }

            if (economyModel.DiamondAmount.Value >= data.diamondPrice)
            {
                var result = await economyModel.AddDiamondAmount(-data.diamondPrice, DisposeCancellationToken);
                if (result.IsOk && interactor != null && LocalPlayer != null)
                {
                    interactablesModel.CreateInteractable(data.viewCode, interactor, true);
                }
            }
            // Show Popup
        }
    }
}