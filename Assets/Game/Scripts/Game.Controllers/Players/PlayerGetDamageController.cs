using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Views.Checkpoints;
using Game.Views.Enemies;
using Game.Views.Levels;
using Game.Views.PlayerEffects;
using Game.Views.Players;
using Modules.Core;
using VContainer;

namespace Game.Controllers.Players
{
    public class PlayerGetDamageController : ControllerBase
    {
        private LevelModel levelModel;
        private IAudioClient audioClient;
        private PlayersModel playersModel;
        private CheckpointsManager checkpointsManager;
        private PlayerEffectsManager playerEffectsManager;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;
        private bool IsInfectedWhenDead => levelModel.LevelConfig.IsInfectedWhenDead;

        [Inject]
        private void Construct(LevelModel levelModel, PlayersModel playersModel, IAudioClient audioClient, CheckpointsManager checkpointsManager, PlayerEffectsManager playerEffectsManager)
        {
            this.levelModel = levelModel;
            this.audioClient = audioClient;
            this.playersModel = playersModel;
            this.checkpointsManager = checkpointsManager;
            this.playerEffectsManager = playerEffectsManager;

            playersModel.LocalPlayer.Where(p => p).Subscribe(HandleLocalPlayer).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayer(PlayerActor player)
        {
            player.OnPlayerDamageReceived.Subscribe(HandlePlayerDamage).AddTo(player);
            player.OnEnemyDamageReceived.Subscribe(HandleEnemyDamage).AddTo(player);
            player.OnLavaDamageReceived.Subscribe(HandleLavaDamage).AddTo(player);
        }

        private void HandlePlayerDamage(PlayerDamageOnPlayerArgs args)
        {
            LocalPlayer.ApplyDamage(args.damage);
            playerEffectsManager.RenderSelfDamageEffect();

            if (!LocalPlayer.IsDead)
            {
                return;
            }

            if (IsInfectedWhenDead)
            {
                if (playersModel.TryGetPlayer(args.killer, out var killerPlayer))
                {
                    if (!LocalPlayer.IsTagged.Value && killerPlayer.IsTagged.Value)
                    {
                        ReviveAsZombie();
                    }
                    else
                    {
                        ReviveDefault();
                    }
                }
            }
            else
            {
                ReviveDefault();
            }
        }

        private void HandleEnemyDamage(EnemyDamageOnPlayerArgs args)
        {
            if (args.enemyId == EnemyId.BossSpider)
            {
                playerEffectsManager.RenderSpiderBossDamageEffect(1);
                UniTaskAsyncEnumerable.Timer(TimeSpan.FromSeconds(2)).Subscribe(_ =>
                {
                    LocalPlayer.ApplyDamage(args.damage);
                    ResolveDeath();
                }).AddTo(DisposeCancellationToken);
            }
            else
            {
                LocalPlayer.ApplyDamage(args.damage);
                playerEffectsManager.RenderSelfDamageEffect();
                ResolveDeath();
            }
        }

        private void HandleLavaDamage(LavaDamageOnPlayerArgs args)
        {
            LocalPlayer.ApplyDamage(args.damage);
            playerEffectsManager.RenderSelfDamageEffect();

            if (LocalPlayer.IsDead)
            {
                ReviveDefault();
            }
        }

        private void ResolveDeath()
        {
            if (LocalPlayer.IsDead)
            {
                if (IsInfectedWhenDead)
                {
                    ReviveAsZombie();
                }
                else
                {
                    ReviveDefault();
                }
            }
        }

        private void ReviveDefault()
        {
            if (checkpointsManager.IsAnyRaceActive)
            {
                playersModel.TeleportLocalPlayer(new PlayerTeleportArgs(checkpointsManager.SpawnPoint.GetPose()));
            }
            else
            {
                playersModel.RespawnLocalPlayer(new PlayerRespawnArgs(true));
            }

            RestoreHealth();
            audioClient.Play(AudioKeys.DeadPlayer, LocalPlayer.HeadNode, LocalPlayer.destroyCancellationToken);
        }

        private void ReviveAsZombie()
        {
            LocalPlayer.SetTagged(true);
            RestoreHealth();
        }

        private void RestoreHealth()
        {
            UniTaskAsyncEnumerable.Timer(TimeSpan.FromSeconds(1)).Subscribe(_ => LocalPlayer.RestoreHealth()).AddTo(LocalPlayer);
        }
    }
}