using Cysharp.Threading.Tasks;
using Game.Core.Data;
using Game.Models;
using Game.Views.Enemies;
using Game.Views.Guns;
using Game.Views.Levels;
using Game.Views.Moderation;
using Game.Views.Monsters;
using Game.Views.Players;
using Game.Views.Weapons;
using Game.Views.Zombies;
using MessagePipe;
using Modules.Core;
using UnityEngine;
using VContainer;
using Weapons_WeaponDamageArgs = Game.Views.Weapons.WeaponDamageArgs;

namespace Game.Controllers.Players
{
    public class PlayerSetDamageController : ControllerBase
    {
        private const int WeaponForce = 1500;
        private const int BulletForce = 3000;

        private VoxelModel voxelModel;
        private LevelModel levelModel;
        private PlayersModel playersModel;
        private ModerationModel moderationModel;

        private bool UseWeaponsDamageOnPlayer => levelModel.LevelConfig.UseWeaponsDamageOnPlayer;
        private bool UseGunsDamageOnPlayer => levelModel.LevelConfig.UseGunsDamageOnPlayer;
        private bool IsDestroyVoxels => levelModel.LevelConfig.IsDestroyVoxels;
        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;

        [Inject]
        private void Construct(
            VoxelModel voxelModel,
            LevelModel levelModel,
            PlayersModel playersModel,
            ModerationModel moderationModel,
            ISubscriber<Weapons_WeaponDamageArgs> weaponDamageSubscriber,
            ISubscriber<RaycastGunDamageArgs> raycastGunDamageSubscriber)
        {
            this.levelModel = levelModel;
            this.voxelModel = voxelModel;
            this.playersModel = playersModel;
            this.moderationModel = moderationModel;

            weaponDamageSubscriber.Subscribe(HandleWeaponDamage).AddTo(DisposeCancellationToken);
            raycastGunDamageSubscriber.Subscribe(HandleRaycastGunDamage).AddTo(DisposeCancellationToken);
        }

        private void HandleWeaponDamage(Weapons_WeaponDamageArgs args)
        {
            var weapon = args.weapon;
            var contact = args.contact;

            if (TryDamagePlayer(contact.otherCollider, out var player) && IsWeaponDamageableForPlayerOrEnemy(weapon))
            {
                if (weapon is ModerationStickActor moderationStick)
                {
                    ModeratePlayer(moderationStick, player);
                }
                else if (UseWeaponsDamageOnPlayer)
                {
                    DamagePlayer(weapon, contact.normal, player);
                }
            }
            else if (TryDamageZombie(contact.otherCollider, out var zombie) && IsWeaponDamageableForPlayerOrEnemy(weapon) && weapon is not ModerationStickActor)
            {
                DamageZombie(weapon, contact.normal, zombie);
            }
            else if (TryDamageMonster(contact.otherCollider, out var monster) && IsWeaponDamageableForPlayerOrEnemy(weapon) && weapon is not ModerationStickActor)
            {
                DamageMonster(weapon, contact.normal, monster);
            }
            else if (TryDamageEnemy(contact.otherCollider, out var enemy) && IsWeaponDamageableForPlayerOrEnemy(weapon) && weapon is not ModerationStickActor)
            {
                DamageEnemy(weapon, contact.normal, enemy);
            }
            else if (IsDestroyVoxels && TryDamageVoxel(contact.otherCollider) && IsWeaponDamageableForVoxel(weapon) && weapon is not ModerationStickActor)
            {
                DamageVoxel(weapon, contact.point, contact.normal);
            }
        }

        private void HandleRaycastGunDamage(RaycastGunDamageArgs args)
        {
            var gun = args.gun;
            var point = args.hit.point;
            var collider = args.hit.collider;

            if (UseGunsDamageOnPlayer && TryDamagePlayer(collider, out var player))
            {
                DamagePlayer(gun.Damage, point, player);
            }
            else if (TryDamageZombie(collider, out var zombie))
            {
                DamageZombie(gun.Damage, point, zombie);
            }
            else if (TryDamageMonster(collider, out var monster))
            {
                DamageMonster(gun.Damage, point, monster);
            }
            else if (TryDamageEnemy(collider, out var enemy))
            {
                DamageEnemy(gun.Damage, point, enemy);
            }
        }

        private bool TryDamagePlayer(Collider target, out PlayerActor player)
        {
            player = null;

            return target.gameObject.layer == Layers.RemotePlayer
                   && target.TryGetComponent(out player)
                   && player.IsAlive;
        }

        private bool TryDamageZombie(Collider target, out ZombieActor zombie)
        {
            zombie = null;
            return target.gameObject.layer == Layers.Enemy && target.TryGetComponent(out zombie) && zombie.IsAlive;
        }

        private bool TryDamageMonster(Collider target, out MonsterActor monster)
        {
            monster = null;
            return target.gameObject.layer == Layers.Enemy && target.TryGetComponent(out monster) && monster.IsAlive;
        }

        private bool TryDamageEnemy(Collider target, out EnemyActor enemy)
        {
            enemy = null;
            return target.gameObject.layer == Layers.Enemy && target.transform.root.TryGetComponent(out enemy) && enemy.IsAlive;
        }

        private bool TryDamageVoxel(Collider target)
        {
            return target.gameObject.layer == Layers.Default;
        }

        private void DamagePlayer(WeaponActor weapon, Vector3 normal, PlayerActor player)
        {
            weapon.PlayHitAudio();
            player.SetDamageByPlayerRpc((byte)weapon.GetDamage(), WeaponForce * -normal);
        }

        private void ModeratePlayer(ModerationStickActor moderationStick, PlayerActor player)
        {
            if (moderationStick.ActiveSentence == ModerationSentence.None)
            {
                return;
            }

            moderationModel.ApplyModeration(new ModerationApplyArgs(moderationStick, player));
        }

        private void DamagePlayer(int damage, Vector3 hitPoint, PlayerActor player)
        {
            var force = BulletForce * (player.HeadNode.position - hitPoint).normalized;
            player.SetDamageByPlayerRpc((byte)damage, force);
        }

        private void DamageZombie(WeaponActor weapon, Vector3 normal, ZombieActor zombie)
        {
            if (zombie.IsKillable(weapon.GetDamage()))
            {
                LocalPlayer.IncrementKilledZombieCount();
            }

            weapon.PlayHitAudio();

            var damageForce = BulletForce * -normal;
            zombie.Damage((byte)weapon.GetDamage(), damageForce);
        }

        private void DamageZombie(int damage, Vector3 hitPoint, ZombieActor zombie)
        {
            if (zombie.IsKillable(damage))
            {
                LocalPlayer.IncrementKilledZombieCount();
            }

            var damageForce = BulletForce * (zombie.Center - hitPoint).normalized;
            zombie.Damage((byte)damage, damageForce);
        }

        private void DamageMonster(WeaponActor weapon, Vector3 normal, MonsterActor monster)
        {
            weapon.PlayHitAudio();

            var damageForce = BulletForce * -normal;
            monster.Damage((byte)weapon.GetDamage(), damageForce);
        }

        private void DamageEnemy(WeaponActor weapon, Vector3 normal, EnemyActor enemy)
        {
            weapon.PlayHitAudio();
            enemy.Damage((byte)weapon.GetDamage());
        }

        private void DamageMonster(int damage, Vector3 hitPoint, MonsterActor monster)
        {
            if (monster.IsKillable(damage))
            {
                LocalPlayer.IncrementKilledZombieCount();
            }

            var damageForce = BulletForce * (monster.Center - hitPoint).normalized;
            monster.Damage((byte)damage, damageForce);
        }

        private void DamageEnemy(int damage, Vector3 hitPoint, EnemyActor enemy)
        {
            enemy.Damage((byte)damage);
        }

        private void DamageVoxel(WeaponActor weapon, Vector3 point, Vector3 normal)
        {
            voxelModel.SetDamage(new VoxelPointDamageArgs((byte)weapon.GetDamage(), point, normal));
        }

        private bool IsWeaponDamageableForPlayerOrEnemy(WeaponActor weapon)
        {
            return weapon.IsGrabbed || (!weapon.IsGrabbed && weapon.CanDamagePlayerWhenDrop && !weapon.IsFirstCollisionAfterDropHappened);
        }

        private bool IsWeaponDamageableForVoxel(WeaponActor weapon)
        {
            return weapon.IsGrabbed || (!weapon.IsGrabbed && weapon.CanDamageVoxelWhenDrop && !weapon.IsFirstCollisionAfterDropHappened);
        }
    }
}