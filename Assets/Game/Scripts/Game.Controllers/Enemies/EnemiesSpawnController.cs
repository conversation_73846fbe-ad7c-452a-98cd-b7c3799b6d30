using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Views.Enemies;
using Game.Views.LevelDefinitions;
using Game.Views.Levels;
using Game.Views.Players;
using Game.Views.Voxels;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using UnityEngine.AI;
using VContainer;
using Random = UnityEngine.Random;

namespace Game.Controllers.Enemies
{
    public class EnemiesSpawnController : ControllerBase
    {
        private LevelModel levelModel;
        private PlayersModel playersModel;
        private INetworkClient networkClient;
        private EnemiesManager enemiesManager;
        private LevelDefinition levelDefinition;
        private VoxelSpaceManager voxelSpaceManager;
        private CancellationTokenSource masterClientCancellationTokenSource;

        private List<EnemySpawnPoint> SpawnPointList => enemiesManager.SpawnPointList;

        [Inject]
        private void Construct(
            LevelModel levelModel,
            PlayersModel playersModel,
            INetworkClient networkClient,
            EnemiesManager enemiesManager,
            VoxelSpaceManager voxelSpaceManager
        )
        {
            this.levelModel = levelModel;
            this.playersModel = playersModel;
            this.networkClient = networkClient;
            this.enemiesManager = enemiesManager;
            this.voxelSpaceManager = voxelSpaceManager;

            networkClient.IsConnected.Subscribe(HandleIsConnected).AddTo(DisposeCancellationToken);
        }

        private void HandleIsConnected(bool ok)
        {
            if (ok)
            {
                PrepareEnemies();
            }
            else
            {
                masterClientCancellationTokenSource.CancelAndDispose();
            }
        }

        private void PrepareEnemies()
        {
            if (!levelModel.HasLevelDefinition)
            {
                return;
            }

            if (SpawnPointList.IsNullOrEmpty())
            {
                return;
            }

            networkClient.IsMasterClient.Subscribe(HandleMasterClient).AddTo(networkClient.DisconnectionCancellationToken);
        }

        private void HandleMasterClient(bool isMasterClient)
        {
            masterClientCancellationTokenSource.CancelAndDispose();

            if (isMasterClient)
            {
                masterClientCancellationTokenSource = new CancellationTokenSource();
                UniTaskAsyncEnumerable.Interval(TimeSpan.FromSeconds(1))
                    .Subscribe(_ => UpdateSpawning())
                    .AddTo(masterClientCancellationTokenSource.Token);
            }
        }

        private void UpdateSpawning()
        {
            for (var i = 0; i < SpawnPointList.Count; i++)
            {
                var spawnPoint = SpawnPointList[i];

                if (!playersModel.TryFindClosestPlayer(spawnPoint.Point, spawnPoint.SpawnTriggerRadius, out _)
                    || enemiesManager.HasEnemyBySpawnIndex((byte)i)
                    || !NavMesh.SamplePosition(spawnPoint.Point, out var hit, 100, NavMesh.AllAreas))
                {
                    continue;
                }

                var pose = new Pose(hit.position, Quaternion.AngleAxis(360 * Random.value, Vector3.up));
                enemiesManager.CreateActor(spawnPoint.EnemyId, pose, i);
            }
        }
    }
}