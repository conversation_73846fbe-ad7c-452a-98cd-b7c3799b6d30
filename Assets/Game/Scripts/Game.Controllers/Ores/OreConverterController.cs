using System;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Models;
using Game.Views.GadgetShop;
using Game.Views.InteractablesCore;
using Game.Views.Items;
using Game.Views.Levels;
using Game.Views.Ores;
using Modules.Core;
using Modules.Network;
using VContainer;

namespace Game.Controllers.Ores
{
    public class OreConverterController : ControllerBase
    {
        private LevelModel levelModel;
        private EconomyModel economyModel;
        private INetworkClient networkClient;
        private GadgetShopConfig gadgetShopConfig;
        private OreConverterManager oreConverterManager;

        [Inject]
        private void Construct(
            LevelModel levelModel,
            EconomyModel economyModel,
            INetworkClient networkClient,
            GadgetShopConfig gadgetShopConfig,
            OreConverterManager oreConverterManager)
        {
            this.levelModel = levelModel;
            this.economyModel = economyModel;
            this.networkClient = networkClient;
            this.gadgetShopConfig = gadgetShopConfig;

            this.oreConverterManager = oreConverterManager;

            levelModel.OnLevelLoaded.Where(ok => ok).Subscribe(_ => HandleLevelLoaded()).AddTo(DisposeCancellationToken);
            networkClient.IsConnected.Where(ok => ok).Subscribe(_ => HandleNetworkConnected()).AddTo(DisposeCancellationToken);
        }

        private void HandleLevelLoaded()
        {
            oreConverterManager.ClearInstances();

            var poses = levelModel.LevelDefinition.PlaceholdersSettings.shredderPlaceholder.transform.GetChildPoses();
            poses.ForEach(p => oreConverterManager.CreateInstance(p));
        }

        private void HandleNetworkConnected()
        {
            if (!levelModel.IsMinesLevel)
            {
                return;
            }

            oreConverterManager.OnInteractableEntered.Subscribe(HandleInteractableEntered).AddTo(networkClient.DisconnectionCancellationToken);
            oreConverterManager.OnBackpackEntered.Subscribe(HandleBackpackEntered).AddTo(networkClient.DisconnectionCancellationToken);
        }

        private void HandleInteractableEntered(InteractableActor interactable)
        {
            if (!interactable.HasStateAuthority)
            {
                return;
            }

            if (gadgetShopConfig.TryGetGadgetShopData(interactable.InteractableId, out var gadgetShopData))
            {
                var diamondCount = gadgetShopData.diamondReward;
                economyModel.AddDiamondAmount(diamondCount, DisposeCancellationToken).Forget();
            }

            networkClient.Despawn(interactable.Object);
        }

        private void HandleBackpackEntered(BackpackActor backpack)
        {
            if (!backpack.HasStateAuthority)
            {
                return;
            }

            var diamondCount = 0;
            foreach (var backpackInventory in backpack.InventoryList)
            {
                if (gadgetShopConfig.TryGetGadgetShopData(backpackInventory.interactableId, out var gadgetShopData))
                {
                    diamondCount += gadgetShopData.diamondReward;
                }
            }

            if (diamondCount > 0)
            {
                economyModel.AddDiamondAmount(diamondCount, DisposeCancellationToken).Forget();
            }

            networkClient.Despawn(backpack.Object);
        }
    }
}