using System;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Game.Models;
using Game.Views.Levels;
using Game.Views.Lobby;
using Game.Views.SmallShop;
using Modules.Core;
using VContainer;

namespace Game.Controllers.Shop
{
    public class SmallShopController : ControllerBase
    {
        private LevelModel levelModel;
        private EconomyModel economyModel;
        private SmallShopManager smallShopManager;
        private LevelSpaceManager levelSpaceManager;
        private LobbySpaceManager lobbySpaceManager;

        [Inject]
        private void Construct(LevelModel levelModel, EconomyModel economyModel, SmallShopManager smallShopManager)
        {
            this.levelModel = levelModel;
            this.economyModel = economyModel;
            this.smallShopManager = smallShopManager;

            levelModel.OnLevelLoaded.Where(ok => ok).Subscribe(_ => HandleLevelLoaded()).AddTo(DisposeCancellationToken);
            economyModel.OnPurchaseCompleted.Subscribe(_ => HandlePurchaseCompeted()).AddTo(DisposeCancellationToken);
        }

        private void HandleLevelLoaded()
        {
            smallShopManager.DestroyAll();

            var poses = levelModel.LevelDefinition.PlaceholdersSettings.playerInventoryPlaceholder.transform.GetChildPoses();
            smallShopManager.Create(poses, economyModel.InventoryList);
        }

        private void HandlePurchaseCompeted()
        {
            smallShopManager.Refresh();
        }
    }
}