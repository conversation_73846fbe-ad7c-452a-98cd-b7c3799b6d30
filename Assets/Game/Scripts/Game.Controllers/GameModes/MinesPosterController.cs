using System;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Game.Views.Levels;
using Modules.Core;
using VContainer;

namespace Game.Controllers.GameModes
{
    public class MinesPosterController : ControllerBase
    {
        [Inject]
        private void Construct(LevelModel levelModel, LevelSpaceManager levelSpaceManager)
        {
            levelModel.OnLevelLoaded.Where(ok => ok).Subscribe(_ =>
            {
                levelSpaceManager.SetAllDecorationsActive(false);
                var levelDecorationsNode = levelSpaceManager.GetLevelDecorationsNode(levelModel.Level.id);
                if (levelDecorationsNode == null)
                {
                    return;
                }

                levelDecorationsNode.SetActive(levelModel.IsMinesLevel);
            }).AddTo(DisposeCancellationToken);
        }
    }
}