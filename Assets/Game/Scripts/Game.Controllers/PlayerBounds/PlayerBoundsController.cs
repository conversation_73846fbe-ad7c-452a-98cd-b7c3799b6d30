using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Views.Checkpoints;
using Game.Views.Levels;
using Game.Views.Players;
using Game.Views.UI.Screens.Notification;
using Modules.Core;
using Modules.Network;
using Modules.UI;
using Modules.XR;
using VContainer;

namespace Game.Controllers.PlayerBounds
{
    public class PlayerBoundsController : ControllerBase
    {
        private IXRPlayer xrPlayer;
        private CheckpointsManager checkpointsManager;
        private LevelModel levelModel;
        private PlayersModel playersModel;
        private NotificationScreen notificationScreen;

        private float softBoundsTime;

        private const float BoundsCheckInterval = 0.5f;

        [Inject]
        private void Construct(
            IXRPlayer xrPlayer,
            PlayersModel playersModel,
            CheckpointsManager checkpointsManager,
            LevelModel levelModel,
            IScreenManager screenManager,
            INetworkClient networkClient)
        {
            this.xrPlayer = xrPlayer;
            this.levelModel = levelModel;
            this.playersModel = playersModel;
            this.checkpointsManager = checkpointsManager;
            notificationScreen = screenManager.GetScreen<NotificationScreen>();

            UniTaskAsyncEnumerable
                .Interval(TimeSpan.FromSeconds(BoundsCheckInterval))
                .Where(_ => networkClient.IsConnected.Value)
                .Subscribe(_ => CheckPlayerBounds())
                .AddTo(DisposeCancellationToken);
        }

        private void CheckPlayerBounds()
        {
            if (IsPlayerOutOfBoundsHard())
            {
                TeleportPlayer();
            }
            else if (IsPlayerOutOfBoundsSoft())
            {
                if (softBoundsTime == 0f)
                {
                    notificationScreen.Show("You are out of bounds.\nTeleporting back in 5 seconds", 4);
                }

                softBoundsTime += BoundsCheckInterval;
                if (softBoundsTime >= 5f)
                {
                    TeleportPlayer();
                }
            }
            else
            {
                if (softBoundsTime > 0)
                {
                    notificationScreen.Show("You are back in bounds", 2);
                }

                softBoundsTime = 0f;
            }
        }

        private void TeleportPlayer()
        {
            softBoundsTime = 0f;
            if (checkpointsManager.IsAnyRaceActive)
            {
                playersModel.TeleportLocalPlayer(new PlayerTeleportArgs(checkpointsManager.SpawnPoint.GetPose()));
            }
            else
            {
                playersModel.RespawnLocalPlayer(new PlayerRespawnArgs(true));
            }
        }

        private bool IsPlayerOutOfBoundsHard()
        {
            var bounds = levelModel.LevelDefinition.BoundsSettings;
            var playerPosition = xrPlayer.HeadNode.position;

            if (playerPosition.y < -bounds.verticalHardLimit ||
                playerPosition.y > bounds.verticalHardLimit ||
                playerPosition.x < -bounds.horizontalHardLimit ||
                playerPosition.x > bounds.horizontalHardLimit ||
                playerPosition.z < -bounds.horizontalHardLimit ||
                playerPosition.z > bounds.horizontalHardLimit)
            {
                return true;
            }

            return false;
        }

        private bool IsPlayerOutOfBoundsSoft()
        {
            var bounds = levelModel.LevelDefinition.BoundsSettings;
            var playerPosition = xrPlayer.HeadNode.position;

            if (playerPosition.y < -bounds.verticalSoftLimit ||
                playerPosition.y > bounds.verticalSoftLimit ||
                playerPosition.x < -bounds.horizontalSoftLimit ||
                playerPosition.x > bounds.horizontalSoftLimit ||
                playerPosition.z < -bounds.horizontalSoftLimit ||
                playerPosition.z > bounds.horizontalSoftLimit)
            {
                return true;
            }

            return false;
        }
    }
}