using UnityEngine;

namespace Game.Views.Enemies
{
    public class EnemySpawnPoint : MonoBehaviour
    {
        [SerializeField] private EnemyId enemyId;
        [SerializeField] private int spawnRadius = 4;
        [SerializeField] private int spawnTriggerRadius = 36;

        public EnemyId EnemyId => enemyId;
        public int SpawnTriggerRadius => spawnTriggerRadius;
        public Vector3 Point => transform.position + GetRandomPoint();

        private Vector3 GetRandomPoint()
        {
            if (spawnRadius == 0)
            {
                return Vector3.zero;
            }

            var randomPosition = spawnRadius * Random.insideUnitCircle;
            return new Vector3(randomPosition.x, 0, randomPosition.y);
        }

        private void OnDrawGizmosSelected()
        {
            Gizmos.matrix = transform.localToWorldMatrix;
            Gizmos.color = new Color(0.51f, 1f, 0.21f, 0.12f);
            Gizmos.DrawSphere(Vector3.zero, spawnTriggerRadius);
        }
    }
}