using System;
using System.Collections.Generic;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Views.LevelDefinitions;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;

namespace Game.Views.Enemies
{
    public class EnemiesManager : MonoBehaviour
    {
        private readonly ISubject<EnemyActor> onDead = new Subject<EnemyActor>();
        private readonly Dictionary<EnemyId, ComponentPool<EnemyView>> enemyViews = new();

        private EnemiesConfig enemiesConfig;
        private INetworkClient networkClient;
        private IObjectResolver objectResolver;

        public List<EnemySpawnPoint> SpawnPointList { get; } = new();
        public IObservable<EnemyActor> OnDead => onDead;

        [Inject]
        private void Construct(INetworkClient networkClient, LevelDefinitionsManager levelDefinitionsManager, IObjectResolver objectResolver, EnemiesConfig enemiesConfig)
        {
            this.enemiesConfig = enemiesConfig;
            this.networkClient = networkClient;
            this.objectResolver = objectResolver;

            levelDefinitionsManager.LevelDefinition.Subscribe(HandleLevelDefinition).AddTo(destroyCancellationToken);
            networkClient.OnNetworkActorSpawned.Subscribe(args => HandleNetworkActorSpawned(args.actor)).AddTo(destroyCancellationToken);
        }

        private void OnDestroy()
        {
            enemyViews.Clear();
        }

        public EnemyActor CreateActor(EnemyId enemyId, Pose pose, int spawnId)
        {
            if (!enemiesConfig.TryGetActorPrefab(enemyId, out var prefab))
            {
                return null;
            }

            EnemyActor enemyActor = null;
            networkClient.Spawn(prefab, pose.position, pose.rotation, (_, obj) =>
            {
                obj.TryGetComponent(out enemyActor);
                enemyActor.SpawnId = (byte)spawnId;
            });
            return enemyActor;
        }

        public EnemyView CreateView(EnemyId enemyId, Transform parent)
        {
            if (!enemyViews.ContainsKey(enemyId) && enemiesConfig.TryGetViewPrefab(enemyId, out var prefab))
            {
                enemyViews[enemyId] = new ComponentPool<EnemyView>(prefab, transform, objectResolver);
            }

            var view = enemyViews[enemyId].Get();
            view.SetParent(parent);
            return view;
        }

        public void DestroyActor(EnemyActor actor)
        {
            if (actor == null)
            {
                return;
            }

            networkClient.Despawn(actor.Object);
        }

        public void DestroyView(EnemyView view)
        {
            if (view == null || !enemyViews.TryGetValue(view.EnemyId, out var pool))
            {
                return;
            }

            pool.Release(view);
        }

        public bool HasEnemyBySpawnIndex(byte spawnIndex)
        {
            return networkClient.TryGetNetworkActorList<EnemyActor>(out var result) && result.Exists(x => x.SpawnId == spawnIndex);
        }

        private void HandleLevelDefinition(LevelDefinition levelDefinition)
        {
            if (levelDefinition == null)
            {
                SpawnPointList.Clear();
            }
            else
            {
                levelDefinition.PlaceholdersSettings.enemyPlaceholder.GetComponentsInChildren(true, SpawnPointList);
            }
        }

        private void HandleNetworkActorSpawned(NetworkActor actor)
        {
            if (actor is not EnemyActor enemy)
            {
                return;
            }

            enemy.OnDead.Subscribe(onDead.OnNext).AddTo(actor);
        }
    }
}