using System.Collections.Generic;
using Fusion;
using Modules.Core;
using UnityEngine;

namespace Game.Views.Enemies
{
    public class EnemiesConfig : Config
    {
        [SerializeField] private List<NetworkObject> actorPrefabList;
        [SerializeField] private List<EnemyView> viewPrefabList;

        public bool TryGetActorPrefab(EnemyId enemyId, out NetworkObject actor)
        {
            actor = actorPrefabList.Find(x => x.GetComponent<EnemyActor>().EnemyId == enemyId);
            return actor != null;
        }

        public bool TryGetViewPrefab(EnemyId enemyId, out EnemyView view)
        {
            view = viewPrefabList.Find(x => x.EnemyId == enemyId);
            return view != null;
        }
    }
}