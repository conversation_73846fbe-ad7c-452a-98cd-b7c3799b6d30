using System;
using System.Collections.Generic;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Fusion;
using Game.Views.Effects;
using Game.Views.Players;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;

namespace Game.Views.Enemies
{
    public class EnemyActor : NetworkActor
    {
        [Header("EnemyActor")]
        [SerializeField] private EnemyId enemyId;
        [SerializeField] private EffectId deathEffectId;
        [SerializeField] private Rigidbody selfRigidbody;
        [SerializeField] private Transform viewNode;
        [SerializeField] private Transform audioNode;
        [SerializeField] private AudioKeys audioKeys;
        [SerializeField] private bool isImmortal;

        [Header("Offsets")]
        [SerializeField] private Vector3 offsetTranslation;
        [SerializeField] private Vector3 offsetRotation;

        private EnemyView view;
        private bool isDeathRendered;
        private bool isDeathEventSent;
        private IAudioClient audioClient;
        private PlayersModel playersModel;
        private EnemiesManager enemiesManager;
        private EffectsManager effectsManager;
        private readonly ISubject<EnemyActor> onDead = new Subject<EnemyActor>();

        public IObservable<EnemyActor> OnDead => onDead;
        public EnemyId EnemyId => enemyId;
        public bool IsAlive => Health > 0;
        public Transform ViewNode => viewNode;
        public Vector3 OffsetTranslation => offsetTranslation;
        public Vector3 OffsetRotation => offsetRotation;

        [Networked] [OnChangedRender(nameof(ChangeState))]
        public EnemyState State { get; set; }

        [Networked] [OnChangedRender(nameof(ChangeHealth))]
        public byte Health { get; set; }

        [Networked] public byte SpawnId { get; set; }

        [Inject]
        private void Construct(IAudioClient audioClient, EffectsManager effectsManager, EnemiesManager enemiesManager, PlayersModel playersModel)
        {
            this.audioClient = audioClient;
            this.playersModel = playersModel;
            this.enemiesManager = enemiesManager;
            this.effectsManager = effectsManager;
        }

        public override void Spawned()
        {
            base.Spawned();
            CreateView();
            ChangeState();
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            base.Despawned(runner, hasState);
            DestroyView();
        }

        public void Damage(byte damage)
        {
            SendRpcSafe(() => DamageRpc(damage));
        }

        public void PlayIdleOrPatrolAudio()
        {
            PlayAudio(audioKeys.Idle);
        }

        public void PlayAttackAudio()
        {
            PlayAudio(audioKeys.attack);
        }

        public void PlayChaseAudio()
        {
            PlayAudio(audioKeys.chase, true);
        }

        public void StopChaseAudio()
        {
            audioClient.Stop(audioKeys.chase);
        }

        private void PlayAudio(string audioKey, bool oneInstance = false)
        {
            if (!IsLocalPlayerInRange() || string.IsNullOrEmpty(audioKey))
            {
                return;
            }

            if (oneInstance)
            {
                audioClient.PlayOneInstance(audioKey, audioNode, destroyCancellationToken);
            }
            else
            {
                audioClient.Play(audioKey, audioNode, destroyCancellationToken);
            }
        }

        private void RenderDeath()
        {
            if (view != null)
            {
                view.SetActive(false);
            }

            PlayAudio(audioKeys.dead);

            if (IsLocalPlayerInRange() && view != null)
            {
                effectsManager.CreateEffect(deathEffectId, view.HeadNode.GetPose(), 2.5f);
            }

            UniTaskAsyncEnumerable
                .Timer(TimeSpan.FromSeconds(1))
                .Subscribe(_ =>
                {
                    if (HasStateAuthority && IsValid)
                    {
                        enemiesManager.DestroyActor(this);
                    }
                })
                .AddTo(destroyCancellationToken);

            isDeathRendered = true;
        }

        private void CreateView()
        {
            DestroyView();
            view = enemiesManager.CreateView(enemyId, viewNode);
        }

        private void DestroyView()
        {
            enemiesManager.DestroyView(view);
            view = null;
        }

        private bool IsLocalPlayerInRange()
        {
            return playersModel.IsLocalPlayerInRadius(transform.position, 10);
        }

        private void ChangeState()
        {
            if (view == null)
            {
                return;
            }

            switch (State)
            {
                case EnemyState.Idle:
                    view.PlayIdleAnimation();
                    break;
                case EnemyState.Patrol:
                    view.PlayWalkAnimation();
                    break;
                case EnemyState.Chase:
                    view.PlayRunAnimation();
                    break;
                case EnemyState.Attack:
                    view.PlayAttackAnimation();
                    break;
            }
        }

        private void ChangeHealth()
        {
            if (Health <= 0 && !isDeathEventSent)
            {
                onDead.OnNext(this);
                isDeathEventSent = true;
            }
        }

        [Rpc(RpcSources.All, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        private void DamageRpc(byte damage)
        {
            if (isDeathRendered)
            {
                return;
            }

            var isDead = !isImmortal && Health - damage <= 0;

            if (HasStateAuthority && !isImmortal)
            {
                Health = (byte)Mathf.Max(0, Health - damage);
            }

            if (isDead)
            {
                RenderDeath();
            }
            else
            {
                PlayAudio(audioKeys.damaged);
            }
        }

        [Serializable]
        private class AudioKeys
        {
            public string chase;
            public string attack;
            public string damaged;
            public string dead;
            [SerializeField] private List<string> idles;

            public string Idle => idles.RandomItem();
        }
    }
}