using Modules.Core;
using UnityEngine;

namespace Game.Views.Enemies
{
    public class EnemyView : Actor
    {
        [SerializeField] private EnemyId enemyId;
        [SerializeField] private Animator selfAnimator;
        [SerializeField] private Transform headNode;

        private static readonly int IdleKey = Animator.StringToHash("Idle");
        private static readonly int RunKey = Animator.StringToHash("Run");
        private static readonly int WalkKey = Animator.StringToHash("Walk");
        private static readonly int AttackKey = Animator.StringToHash("Attack");
        private static readonly int DamagedKey = Animator.StringToHash("GetHit");

        public EnemyId EnemyId => enemyId;
        public Transform HeadNode => headNode;

        public void PlayIdleAnimation()
        {
            PlayAnimation(IdleKey);
        }

        public void PlayRunAnimation()
        {
            PlayAnimation(RunKey);
        }

        public void PlayWalkAnimation()
        {
            PlayAnimation(WalkKey);
        }

        public void PlayAttackAnimation()
        {
            PlayAnimation(AttackKey);
        }

        public void PlayDamagedAnimation()
        {
            PlayAnimation(DamagedKey);
        }

        private void PlayAnimation(int key)
        {
            selfAnimator.Rebind();
            selfAnimator.SetTrigger(key);
        }
    }
}