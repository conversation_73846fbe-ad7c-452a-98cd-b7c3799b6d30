using System;
using System.Reactive.Subjects;
using Fusion;
using Game.Core;
using Game.Views.Enemies;
using UnityEngine;

namespace Game.Views.Players
{
    public partial class PlayerActor
    {
        private readonly ISubject<PlayerDamageOnPlayerArgs> onPlayerDamageReceived = new Subject<PlayerDamageOnPlayerArgs>();
        private readonly ISubject<EnemyDamageOnPlayerArgs> onEnemyDamageReceived = new Subject<EnemyDamageOnPlayerArgs>();
        private readonly ISubject<LavaDamageOnPlayerArgs> onLavaDamageReceived = new Subject<LavaDamageOnPlayerArgs>();

        public IObservable<PlayerDamageOnPlayerArgs> OnPlayerDamageReceived => onPlayerDamageReceived;
        public IObservable<EnemyDamageOnPlayerArgs> OnEnemyDamageReceived => onEnemyDamageReceived;
        public IObservable<LavaDamageOnPlayerArgs> OnLavaDamageReceived => onLavaDamageReceived;

        [Rpc(RpcSources.All, RpcTargets.StateAuthority, Channel = RpcChannel.Unreliable)]
        public void SetDamageByPlayerRpc(byte damage, Vector3 force, RpcInfo info = default)
        {
            if (IsDead)
            {
                return;
            }

            onPlayerDamageReceived.OnNext(new PlayerDamageOnPlayerArgs(info.Source, damage, force));
        }

        [Rpc(RpcSources.All, RpcTargets.StateAuthority, Channel = RpcChannel.Unreliable)]
        public void SetDamageByZombieRpc(byte damage)
        {
            if (IsDead)
            {
                return;
            }

            audioClient.Play(AudioKeys.ZombieAttack, HeadNode, destroyCancellationToken);
            onEnemyDamageReceived.OnNext(new EnemyDamageOnPlayerArgs(damage));
        }

        [Rpc(RpcSources.All, RpcTargets.StateAuthority, Channel = RpcChannel.Unreliable)]
        public void SetDamageByMonsterRpc(byte damage)
        {
            if (IsDead)
            {
                return;
            }

            onEnemyDamageReceived.OnNext(new EnemyDamageOnPlayerArgs(damage));
        }

        public void SetDamageByLava(byte damage)
        {
            if (!HasStateAuthority || IsDead)
            {
                return;
            }

            audioClient.Play(AudioKeys.LavaBurn, HeadNode, destroyCancellationToken);
            onLavaDamageReceived.OnNext(new LavaDamageOnPlayerArgs(damage));
        }

        public void SetDamageByBossSpider()
        {
            SendRpcSafe(() => SetDamageByBossSpiderRpc(StateAuthority));
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        private void SetDamageByBossSpiderRpc([RpcTarget] PlayerRef playerRef)
        {
            if (IsDead)
            {
                return;
            }

            onEnemyDamageReceived.OnNext(new EnemyDamageOnPlayerArgs(playersConfig.MaxHealth, EnemyId.BossSpider));
        }
    }
}