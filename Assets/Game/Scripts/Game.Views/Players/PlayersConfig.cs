using Fusion;
using Modules.Core;
using UnityEngine;

namespace Game.Views.Players
{
    public class PlayersConfig : Config
    {
        [SerializeField] private OfflinePlayerActor offlinePlayerPrefab;
        [SerializeField] private NetworkObject networkPlayerPrefab;
        [SerializeField] private Material infectionMaterial;
        [SerializeField] private byte maxHealth;
        [SerializeField] private int grenadeLauncherAmmo;

        [SerializeField] private float scaleStep = 0.5f;
        [SerializeField] private float minScale = 0.5f;
        [SerializeField] private float maxScale = 10f;

        public OfflinePlayerActor OfflinePlayerPrefab => offlinePlayerPrefab;
        public NetworkObject NetworkPlayerPrefab => networkPlayerPrefab;
        public Material InfectionMaterial => infectionMaterial;
        public byte MaxHealth => maxHealth;
        public int GrenadeLauncherAmmo => grenadeLauncherAmmo;
        public float ScaleStep => scaleStep;
        public float MinScale => minScale;
        public float MaxScale => maxScale;
    }
}