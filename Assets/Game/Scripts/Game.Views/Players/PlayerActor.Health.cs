using System;
using System.Reactive;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Fusion;
using Game.Core;
using UnityEngine;

namespace Game.Views.Players
{
    public partial class PlayerActor
    {
        private readonly ISubject<int> onMedicalConsumed = new Subject<int>();
        private readonly ISubject<Unit> onBeforeDeadLocal = new Subject<Unit>();
        private readonly ISubject<Unit> onDead = new Subject<Unit>();
        private readonly IAsyncReactiveProperty<int> health = new AsyncReactiveProperty<int>(0);

        public IReadOnlyAsyncReactiveProperty<int> Health => health;

        public bool IsAlive => HealthNetworked > 0;
        public bool IsDead => !IsAlive;
        public IObservable<int> OnMedicalConsumed => onMedicalConsumed;
        public IObservable<Unit> OnBeforeDeadLocal => onBeforeDeadLocal;
        public IObservable<Unit> OnDead => onDead;

        [Networked] [OnChangedRender(nameof(ChangeHealthNetworked))]
        private byte HealthNetworked { get; set; }

        [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
        public void ConsumeMedicalRpc(byte health)
        {
            audioClient.Play(AudioKeys.ConsumeMedical, HeadNode.position, destroyCancellationToken);
            onMedicalConsumed.OnNext(health);
        }

        public bool IsKillable(int damage)
        {
            return HealthNetworked <= damage;
        }

        public void ApplyDamage(int damage)
        {
            var health = (byte)Mathf.Max(0, HealthNetworked - damage);
            if (health == 0)
            {
                onBeforeDeadLocal.OnNext(Unit.Default);
            }

            HealthNetworked = health;
        }

        public void ApplyHeal(int health)
        {
            HealthNetworked = (byte)Mathf.Min(HealthNetworked + health, playersConfig.MaxHealth);
        }

        public void RestoreHealth()
        {
            HealthNetworked = playersConfig.MaxHealth;
        }

        public void SetActiveHealthSystem(bool isActive)
        {
            playerView.SetActiveHealthObject(HasStateAuthority, isActive);
        }

        private void ChangeHealthNetworked()
        {
            if (health.Value != HealthNetworked)
            {
                health.Value = HealthNetworked;
            }

            if (IsDead)
            {
                onDead.OnNext(Unit.Default);
            }

            if (HasStateAuthority && IsDead)
            {
                audioClient.Play(AudioKeys.DeadPlayer, HeadNode, destroyCancellationToken);
            }

            playerView.SetPlayerHealth(HasStateAuthority, HealthNetworked, playersConfig.MaxHealth);
        }
    }
}