using System.Collections.Generic;
using Modules.Core;
using UnityEngine;
using VoxelPlay;

namespace Game.Views.Voxels
{
    public class VoxelConfig : Config
    {
        [SerializeField] private float raycastDistance;
        [SerializeField] private float playingSoundSqrDistance;
        [SerializeField] private float gridSize;
        [SerializeField] private int worldExtent;
        [SerializeField] private int worldHeight;
        [SerializeField] private string voxelTouchAudioKey;
        [SerializeField] private string voxelClimbAudioKey;
        [SerializeField] private VoxelPreviewActor voxelPreviewPrefab;
        [SerializeField] private List<BlockData> playerVoxelDataList;
        [SerializeField] private List<BlockData> otherVoxelDataList;
        [SerializeField] private List<VoxelDefinition> lavaVoxels;
        [SerializeField] private List<VoxelDefinition> waterVoxels;

        [Header("Post processing settings")]
        [SerializeField] private GameObject lavaVolume;
        [SerializeField] private GameObject waterVolume;

        private List<BlockData> allBlockDataList;
        private VoxelDefinition[] allVoxelDefList;

        public float RaycastDistance => raycastDistance;
        public float PlayingSoundSqrDistance => playingSoundSqrDistance;
        public float GridSize => gridSize;
        public int WorldExtent => worldExtent;
        public int WorldHeight => worldHeight;
        public VoxelPreviewActor VoxelPreviewPrefab => voxelPreviewPrefab;
        public List<BlockData> PlayerBlockDataList => playerVoxelDataList;
        public List<BlockData> OtherBlockDataList => otherVoxelDataList;
        public List<BlockData> AllBlockDataList => allBlockDataList ??= GetAllBlockDataList();
        public List<VoxelDefinition> LavaVoxels => lavaVoxels;
        public List<VoxelDefinition> WaterVoxels => waterVoxels;
        public string VoxelTouchAudioKey => voxelTouchAudioKey;
        public string VoxelClimbAudioKey => voxelClimbAudioKey;
        public GameObject LavaVolume => lavaVolume;
        public GameObject WaterVolume => waterVolume;
        public VoxelDefinition[] AllVoxelDefList => allVoxelDefList ??= GetAllVoxelDefList();

        private void OnValidate()
        {
            allVoxelDefList = null;
            allBlockDataList = null;
        }

        public bool TryGetVoxelDefId(VoxelDefinition voxel, out int id)
        {
            var blockData = playerVoxelDataList.Find(x => x.voxel == voxel);
            if (blockData != null)
            {
                id = blockData.id;
                return true;
            }

            blockData = otherVoxelDataList.Find(x => x.voxel == voxel);
            if (blockData != null)
            {
                id = blockData.id;
                return true;
            }

            id = 0;
            return false;
        }

        public bool TryGetVoxelDef(int id, out VoxelDefinition voxelDef)
        {
            var blockData = playerVoxelDataList.Find(x => x.id == id);
            if (blockData != null)
            {
                voxelDef = blockData.voxel;
                return true;
            }

            blockData = otherVoxelDataList.Find(x => x.id == id);
            if (blockData != null)
            {
                voxelDef = blockData.voxel;
                return true;
            }

            voxelDef = null;
            return false;
        }

        public bool TryGetBlockData(VoxelDefinition voxelDef, out BlockData blockData)
        {
            blockData = playerVoxelDataList.Find(x => x.voxel == voxelDef);
            if (blockData != null)
            {
                return true;
            }

            blockData = otherVoxelDataList.Find(x => x.voxel == voxelDef);
            if (blockData != null)
            {
                return true;
            }

            blockData = null;
            return false;
        }

        public bool TryGetBlockData(int id, out BlockData blockData)
        {
            blockData = playerVoxelDataList.Find(x => x.id == id);
            if (blockData != null)
            {
                return true;
            }

            blockData = otherVoxelDataList.Find(x => x.id == id);
            if (blockData != null)
            {
                return true;
            }

            blockData = null;
            return false;
        }

        public bool IsInfiniteVoxelDef(int id)
        {
            return PlayerBlockDataList.Find(x => x.id == id)?.IsInfinite ?? false;
        }

        public bool IsInfiniteVoxelDef(string name)
        {
            return PlayerBlockDataList.Find(x => x.voxel.name == name)?.IsInfinite ?? false;
        }

        private VoxelDefinition[] GetAllVoxelDefList()
        {
            var buffer = new List<VoxelDefinition>();
            buffer.AddRange(PlayerBlockDataList.ConvertAll(x => x.voxel));
            buffer.AddRange(OtherBlockDataList.ConvertAll(x => x.voxel));
            return buffer.ToArray();
        }

        private List<BlockData> GetAllBlockDataList()
        {
            var buffer = new List<BlockData>();
            buffer.AddRange(PlayerBlockDataList);
            buffer.AddRange(OtherBlockDataList);
            return buffer;
        }
    }
}