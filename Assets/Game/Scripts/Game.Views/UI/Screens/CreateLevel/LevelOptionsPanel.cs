using System;
using System.Collections.Generic;
using System.Reactive;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Core.Data;
using Game.Views.LevelDefinitions;
using Game.Views.UI.Shared;
using Game.Views.Voxels;
using Modules.Core;
using Modules.UI;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Views.UI.Screens.CreateLevel
{
    public class LevelOptionsPanel : GameScreen
    {
        [SerializeField] private TextWidget mapNameText;
        [SerializeField] private Image mapImage;
        [SerializeField] private PokeableButton prevMapButton;
        [SerializeField] private PokeableButton nextMapButton;
        [SerializeField] private PokeableButton createLevelButton;
        [SerializeField] private PokeableButton backButton;
        [SerializeField] private List<GameModeToggle> gameModeToggleList;

        private readonly ISubject<Unit> onCreateLevelClicked = new Subject<Unit>();
        private readonly ISubject<Unit> onBackClicked = new Subject<Unit>();
        private CancellationTokenSource disableCancellationTokenSource;
        private List<LevelDefinition> levelDefinitionList;
        private int selectedWorldTemplateIndex;

        public LevelDefinitionId LevelDefinitionId => levelDefinitionList[selectedWorldTemplateIndex].Id;
        public GameMode GameMode => gameModeToggleList.Find(x => x.IsOn)?.GameMode ?? GameMode.Sandbox;
        public IObservable<Unit> OnCreateLevelClicked => onCreateLevelClicked;
        public IObservable<Unit> OnBackClicked => onBackClicked;

        protected override void OnEnable()
        {
            base.OnEnable();

            disableCancellationTokenSource = new CancellationTokenSource();
            var token = disableCancellationTokenSource.Token;

            prevMapButton.OnClick.Subscribe(HandlePrevMapButton).AddTo(token);
            nextMapButton.OnClick.Subscribe(HandleNextMapButton).AddTo(token);
            gameModeToggleList.ForEach(x => x.OnValueChanged.Where(y => y.IsOn).Subscribe(z => HandleGameModeUpdated(z as GameModeToggle)).AddTo(token));
            backButton.OnClick.Subscribe(HandleBackButton).AddTo(token);
            createLevelButton.OnClick.Subscribe(HandleCreateLevelButton).AddTo(token);
        }

        protected override void OnDisable()
        {
            base.OnDisable();

            disableCancellationTokenSource.CancelAndDispose();
        }

        public void Initialize(List<LevelDefinition> levelDefinitionList)
        {
            this.levelDefinitionList = levelDefinitionList;
            SetWorldDefinition(0);
        }

        public void ResetPanel()
        {
            gameModeToggleList.FindAll(x => x.IsOn).ForEach(x => x.SetValue(false));
            gameModeToggleList[0].SetValue(true);
        }

        private void SetWorldDefinition(int shift)
        {
            selectedWorldTemplateIndex = (selectedWorldTemplateIndex + levelDefinitionList.Count + shift) % levelDefinitionList.Count;
            mapNameText.SetMessage(levelDefinitionList[selectedWorldTemplateIndex].SharedSettings.title);
            mapImage.sprite = levelDefinitionList[selectedWorldTemplateIndex].SharedSettings.icon;
        }

        private void HandlePrevMapButton(PokeableButton button)
        {
            SetWorldDefinition(-1);
        }

        private void HandleNextMapButton(PokeableButton button)
        {
            SetWorldDefinition(1);
        }

        private void HandleGameModeUpdated(GameModeToggle toggle)
        {
            gameModeToggleList.FindAll(x => x != toggle).ForEach(x => x.SetValue(false));
        }

        private void HandleCreateLevelButton(PokeableButton button)
        {
            disableCancellationTokenSource.CancelAndDispose();
            onCreateLevelClicked.OnNext(Unit.Default);
        }

        private void HandleBackButton(PokeableButton button)
        {
            onBackClicked.OnNext(Unit.Default);
        }
    }
}