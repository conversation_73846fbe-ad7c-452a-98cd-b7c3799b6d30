using System;
using System.Reactive;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Core.Data;
using Game.Views.Extensions;
using Game.Views.LevelDefinitions;
using Game.Views.Voxels;
using Modules.Core;
using Modules.UI;
using UnityEngine;
using VContainer;

namespace Game.Views.UI.Screens.CreateLevel
{
    public class CreateLevelScreen : GameScreen
    {
        [SerializeField] private LevelOptionsPanel levelOptionsPanel;
        [SerializeField] private LevelNamePanel levelNamePanel;
        [SerializeField] private LoadingPanel loadingPanel;
        [SerializeField] private ModalPanel modalPanel;
        [SerializeField] private GameObject mainPanel;
        [SerializeField] private ObjectActivation canvasObject;

        private VoxelConfig voxelConfig;
        private CancellationTokenSource disableCancellationTokenSource;
        private readonly ISubject<CreateLevelArgs> onCreateLevelClicked = new Subject<CreateLevelArgs>();

        public IObservable<CreateLevelArgs> OnCreateLevelClicked => onCreateLevelClicked;

        private void Awake()
        {
            ResetScreen();
        }

        protected override void OnEnable()
        {
            base.OnEnable();

            disableCancellationTokenSource = new CancellationTokenSource();
            var token = disableCancellationTokenSource.Token;

            levelNamePanel.OnNextClicked.Subscribe(HandleToLevelOptionsPanelClicked).AddTo(token);

            levelOptionsPanel.OnCreateLevelClicked.Subscribe(HandleCreateLevelClicked).AddTo(token);
            levelOptionsPanel.OnBackClicked.Subscribe(HandleToLevelNamePanelClicked).AddTo(token);
        }

        protected override void OnDisable()
        {
            base.OnDisable();

            disableCancellationTokenSource.CancelAndDispose();
        }

        [Inject]
        private void Construct(LevelDefinitionsConfig levelDefinitionsConfig)
        {
            levelOptionsPanel.Initialize(levelDefinitionsConfig.GetPreviewLevelDefinitionList());
        }

        public void ResetScreen()
        {
            levelOptionsPanel.ResetPanel();
            levelNamePanel.ResetPanel();
            levelOptionsPanel.Hide();
            levelNamePanel.Show();
        }

        public void SetActiveLoadingState(bool isActive)
        {
            loadingPanel.SetActive(isActive);
            mainPanel.SetActive(!isActive);
        }

        public void RenderInfo(string message, int timeout = 5)
        {
            if (!canvasObject.IsActive.Value)
            {
                return;
            }

            SetActiveModalPanel(true);
            modalPanel.Render(message).WithTimeout(timeout, () => SetActiveModalPanel(false));
        }

        private void SetActiveModalPanel(bool isActive)
        {
            modalPanel.SetActive(isActive);
            mainPanel.SetActive(!isActive);
        }

        private void HandleToLevelOptionsPanelClicked(Unit unit)
        {
            levelOptionsPanel.Show();
            levelNamePanel.Hide();
        }

        private void HandleToLevelNamePanelClicked(Unit unit)
        {
            levelOptionsPanel.Hide();
            levelNamePanel.Show();
        }

        private void HandleCreateLevelClicked(Unit unit)
        {
            onCreateLevelClicked.OnNext(new CreateLevelArgs(
                levelNamePanel.LevelName,
                levelNamePanel.LevelPassword,
                (int)levelOptionsPanel.LevelDefinitionId,
                levelOptionsPanel.GameMode,
                voxelConfig.WorldExtent));
        }
    }
}