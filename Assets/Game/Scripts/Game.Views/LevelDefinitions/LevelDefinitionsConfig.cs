using System.Collections.Generic;
using Game.Core.Data;
using Modules.Core;
using UnityEngine;

namespace Game.Views.LevelDefinitions
{
    public class LevelDefinitionsConfig : Config
    {
        [SerializeField] private List<LevelDefinition> levelDefinitionList;

        public bool TryGetPrefab(LevelDefinitionId id, out LevelDefinition prefab)
        {
            prefab = levelDefinitionList.Find(x => x.Id == id);
            return prefab != null;
        }

        public List<LevelDefinition> GetPreviewLevelDefinitionList()
        {
            return levelDefinitionList.FindAll(l => !l.SharedSettings.previewDisabled);
        }

        public Sprite GetIcon(LevelDefinitionId id)
        {
            return levelDefinitionList.Find(l => l.Id == id)?.SharedSettings.icon;
        }
    }
}