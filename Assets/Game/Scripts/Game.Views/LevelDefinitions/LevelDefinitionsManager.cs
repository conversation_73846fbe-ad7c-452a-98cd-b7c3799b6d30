using Cysharp.Threading.Tasks;
using Game.Core.Data;
using UnityEngine;
using VContainer;

namespace Game.Views.LevelDefinitions
{
    public class LevelDefinitionsManager : MonoBehaviour
    {
        [SerializeField] private LevelDefinitionId defaultLevelDefinitionId;

        private LevelDefinitionsConfig config;
        private readonly IAsyncReactiveProperty<LevelDefinition> levelDefinition = new AsyncReactiveProperty<LevelDefinition>(null);

        public bool HasLevelDefinition => LevelDefinition.Value != null;
        public IReadOnlyAsyncReactiveProperty<LevelDefinition> LevelDefinition => levelDefinition;

        [Inject]
        private void Construct(LevelDefinitionsConfig config)
        {
            this.config = config;
        }

        private void Awake()
        {
            CreateDefaultLevelDefinition();
        }

        public void CreateLevelDefinition(LevelDefinitionId id)
        {
            DestroyLevelDefinition();

            if (!config.TryGetPrefab(id, out var prefab))
            {
                CreateDefaultLevelDefinition();
                return;
            }

            var levelDefinitionValue = Instantiate(prefab);
            SetLevelDefinition(levelDefinitionValue);
        }

        public void CreateDefaultLevelDefinition()
        {
            CreateLevelDefinition(defaultLevelDefinitionId);
        }

        public void DestroyLevelDefinition()
        {
            if (!HasLevelDefinition)
            {
                return;
            }

            Destroy(LevelDefinition.Value.gameObject);
            SetLevelDefinition(null);
        }

        private void SetLevelDefinition(LevelDefinition levelDefinitionValue)
        {
            if (levelDefinition.Value == levelDefinitionValue)
            {
                return;
            }

            levelDefinition.Value = levelDefinitionValue;
        }
    }
}