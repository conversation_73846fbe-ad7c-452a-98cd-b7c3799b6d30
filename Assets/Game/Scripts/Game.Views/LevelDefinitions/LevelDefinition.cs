using System;
using Game.Core.Data;
using Modules.Core;
using UnityEngine;
using VoxelPlay;

namespace Game.Views.LevelDefinitions
{
    public class LevelDefinition : Actor
    {
        [SerializeField] private LevelDefinitionId id;
        [SerializeField] private Shared sharedSettings;
        [SerializeField] private Bounds boundsSettings;
        [SerializeField] private Placeholders placeholdersSettings;
        [SerializeField] private VoxelEnvironment voxelEnvironmentSettings;

        public LevelDefinitionId Id => id;
        public Shared SharedSettings => sharedSettings;
        public Bounds BoundsSettings => boundsSettings;
        public Placeholders PlaceholdersSettings => placeholdersSettings;
        public VoxelEnvironment VoxelEnvironmentSettings => voxelEnvironmentSettings;

        [Serializable]
        public class Shared
        {
            [SerializeField] public string title;
            [SerializeField] public string audioKey;
            [SerializeField] public Sprite icon;
            [SerializeField] public bool previewDisabled;
        }

        [Serializable]
        public class Bounds
        {
            [SerializeField] public bool boundsDisabled;
            [SerializeField] public Vector3 worldExtents;
            [SerializeField] public Vector3 worldCenter;
            [SerializeField] public float verticalSoftLimit;
            [SerializeField] public float verticalHardLimit;
            [SerializeField] public float horizontalSoftLimit;
            [SerializeField] public float horizontalHardLimit;
        }

        [Serializable]
        public class Placeholders
        {
            [SerializeField] public GameObject enemyPlaceholder;
            [SerializeField] public GameObject gadgetShopPlaceholder;
            [SerializeField] public GameObject blockShopPlaceholder;
            [SerializeField] public GameObject playerInventoryPlaceholder;
            [SerializeField] public GameObject shredderPlaceholder;
            [SerializeField] public GameObject mirrorPlaceholder;
            [SerializeField] public GameObject atmPlaceholder;
        }

        [Serializable]
        public class VoxelEnvironment
        {
            [Header("WorldDefinition")]
            public WorldDefinition worldDefinition;
            [SerializeField] public VoxelDefinition defaultVoxelDefinition;

            [Header("Generation")]
            public int visibleChunkDistance;
            public bool useNavMesh;
            public bool unloadFarNavMesh;
            public bool useTrees;
            public bool useVegetation;
            public bool useDetailGeneration;

            [Header("Lighting")]
            public float ambientLight;
            public float daylightShadowAtten;
            public float diffuseWrap;
            public Color ambientColor;

            [Header("Sun")]
            public float sunIntensity;
            public Color sunColor;

            [Header("Fog")]
            public bool useFog;
            public float fogHeight;
            public float fogDistance;
            public float fogFallOff;
            public Color fogTint;

            [Header("MapData")]
            public TextAsset mapData;
        }
    }
}