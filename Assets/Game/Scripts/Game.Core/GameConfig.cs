using System;
using System.Collections.Generic;
using Fusion;
using Game.Core.Data;
using Modules.Core;
using UnityEngine;

namespace Game.Core
{
    public class GameConfig : Config
    {
        [Header("Levels")]
        [SerializeField] private string lobbyLevelId;
        [SerializeField] private string minesLevelId;
        [SerializeField] private string darkForestId;

        [Header("Lobby")]
        [SerializeField] private string customPortalLevelId;
        [SerializeField] private List<LobbyPortalData> lobbyPortalList;

        [Header("Network")]
        [SerializeField] private int maxPlayerCount;
        [SerializeField] private int targetPlayerCount;
        [SerializeField] private bool networkAuthEnabled;
        [SerializeField] private int closeNetworkSessionTime;
        [SerializeField] private NetworkObject masterClientObjectPrefab;

        [Header("Voxels")]
        [SerializeField] private int sliceVoxelsInterval;

        [Header("Audio")]
        [SerializeField] private List<AudioCollection> audioCollectionList;

        [Head<PERSON>("Game Modes")]
        [SerializeField] private CrownModeConfig crownMode;

        [Header("Shared")]
        [SerializeField] private bool videoRecorderEnabled;
        [SerializeField] private int moneyBagAmount;
        [SerializeField] private string privateLobbyName;

        [Header("Interactables")]
        [SerializeField] private int defaultDropTimeout;
        [SerializeField] private int minesDropTimeout;
        [SerializeField] private int defaultMaxInteractablesPerPlayer;
        [SerializeField] private int minesMaxInteractablesPerPlayer;

        [Header("Configs")]
        [SerializeField] private DebugConfig debugConfig;

        public int MaxPlayerCount
        {
            get => maxPlayerCount;
            set => maxPlayerCount = Mathf.Max(10, value);
        }
        public int TargetPlayerCount
        {
            get => targetPlayerCount;
            set => targetPlayerCount = Mathf.Max(10, value);
        }
        public int CloseNetworkSessionTime
        {
            get => closeNetworkSessionTime;
            set => closeNetworkSessionTime = Mathf.Max(0, value);
        }
        public bool NetworkAuthEnabled
        {
            get => networkAuthEnabled;
            set => networkAuthEnabled = value;
        }
        public bool VideoRecorderEnabled
        {
            get => videoRecorderEnabled;
            set => videoRecorderEnabled = value;
        }
        public string CustomPortalLevelId
        {
            get => customPortalLevelId;
            set => customPortalLevelId = value;
        }

        public DebugConfig DebugConfig => debugConfig;
        public string LobbyLevelId => lobbyLevelId;
        public string MinesLevelId => minesLevelId;
        public string DarkForestId => darkForestId;
        public List<LobbyPortalData> LobbyPortalList => lobbyPortalList;
        public int SliceVoxelsInterval => sliceVoxelsInterval;
        public NetworkObject MasterClientObjectPrefab => masterClientObjectPrefab;
        public List<AudioCollection> AudioCollectionList => audioCollectionList;
        public int MoneyBagAmount => moneyBagAmount;
        public CrownModeConfig CrownMode => crownMode;
        public string PrivateLobbyName => privateLobbyName;
        public int DefaultDropTimeout => defaultDropTimeout;
        public int MinesDropTimeout => minesDropTimeout;
        public int DefaultMaxInteractablesPerPlayer => defaultMaxInteractablesPerPlayer;
        public int MinesMaxInteractablesPerPlayer => minesMaxInteractablesPerPlayer;

        public void SetPrivateLobbyName(string privateLobbyId)
        {
            privateLobbyName = privateLobbyId;
        }

        [Serializable]
        public class CrownModeConfig
        {
            [SerializeField] private int roundDuration;
            [SerializeField] private int pauseDuration;

            public int RoundDuration
            {
                get => roundDuration;
                set => roundDuration = Mathf.Max(30, value);
            }
            public int PauseDuration
            {
                get => pauseDuration;
                set => pauseDuration = Mathf.Max(10, value);
            }
        }
    }
}