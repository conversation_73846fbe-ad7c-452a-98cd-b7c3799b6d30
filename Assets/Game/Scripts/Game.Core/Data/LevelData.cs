using System;
using System.Collections.Generic;
using UnityEngine;

namespace Game.Core.Data
{
    [Serializable]
    public class LevelData
    {
        public string id;
        public string name;
        public string createdBy;
        public string createdByName;
        public int worldTemplateId;
        public int worldExtents;
        public GameMode gameMode;
        public LevelDefinitionId levelDefinitionId;
        public bool isPublished;
        public bool buildingDisabled;
        public Vector3 worldPortalPosition;
        public List<LocomotionAreaData> locomotionAreas;
        public List<BuildingAreaData> buildingAreas;
        public List<CheckpointListData> checkpoints;
        public List<MonsterData> monsters;
        public bool useBlockInventory;
    }
}