using Game.Core;
using Game.View.Locomotions;
using Game.Views.Enemies;
using Game.Views.GadgetShop;
using Game.Views.InteractablesCore;
using Game.Views.InteractableSlots;
using Game.Views.LevelDefinitions;
using Game.Views.Levels;
using Game.Views.Network;
using Game.Views.Scenes;
using Game.Views.Storage;
using Game.Views.Voxels;
using Modules.Core;
using UnityEngine;
using VContainer;
using VContainer.Unity;

namespace Game.Main
{
    public class GameMonoInstaller : MonoInstaller
    {
        [Header("Shared")]
        [SerializeField] private LocomotionSystem locomotionSystemPrefab;

        [Header("Configs")]
        [SerializeField] private GameConfig gameConfig;
        [SerializeField] private VoxelConfig voxelConfig;
        [SerializeField] private LevelsConfig levelsConfig;
        [SerializeField] private EnemiesConfig enemiesConfig;
        [SerializeField] private GadgetShopConfig gadgetShopConfig;
        [SerializeField] private LevelDefinitionsConfig levelDefinitionsConfig;

        public override void Install(IContainerBuilder builder, Transform node = null)
        {
            builder.RegisterEntryPoint<GameEntry>();
            builder.Register<IScenesManager, ScenesManager>(Lifetime.Singleton);
            builder.Register<ILocalStorage, LocalStorage>(Lifetime.Singleton);
            builder.Register<ControllerManager>(Lifetime.Singleton);
            builder.Register<StateMachine>(Lifetime.Singleton);
            builder.Register<NetworkWire>(Lifetime.Singleton);
            builder.Register<NetworkEventBus>(Lifetime.Singleton);
            builder.Register<SharedInteractableSettings>(Lifetime.Singleton);

            // Shared
            builder.RegisterComponentInNewPrefab(locomotionSystemPrefab, Lifetime.Singleton);

            // Voxels
            builder.Register<VoxelSpaceManager>(Lifetime.Singleton);
            builder.RegisterInstance(voxelConfig);

            // Configs
            builder.RegisterInstance(gameConfig);
            builder.RegisterInstance(levelsConfig);
            builder.RegisterInstance(enemiesConfig);
            builder.RegisterComponent(gadgetShopConfig);
            builder.RegisterComponent(levelDefinitionsConfig);

            // Interactable Slots
            builder.RegisterComponentInHierarchy<InteractableSlotsManager>();
        }
    }
}